@extends('layouts.app')

@section('title', 'Email Marketing Dashboard')

@section('content')
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0 text-primary">
                        <i class="fas fa-envelope me-2"></i>Email Marketing Dashboard
                    </h1>
                    <p class="text-muted mb-0">Quản lý chiến dịch email marketing và theo dõi hiệu suất</p>
                </div>
                <div>
                    <a href="{{ route('email-marketing.campaigns.create') }}" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Tạo Chiến Dịch Mới
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                <div class="card-body text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-50 small">Tổng Chiến Dịch</div>
                            <div class="h2 mb-0">{{ $totalCampaigns }}</div>
                        </div>
                        <div class="fa-2x opacity-50">
                            <i class="fas fa-bullhorn"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="card-body text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-50 small">Đang Hoạt Động</div>
                            <div class="h2 mb-0">{{ $activeCampaigns }}</div>
                        </div>
                        <div class="fa-2x opacity-50">
                            <i class="fas fa-play-circle"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="card-body text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-50 small">Email Đã Gửi</div>
                            <div class="h2 mb-0">{{ number_format($totalEmailsSent) }}</div>
                        </div>
                        <div class="fa-2x opacity-50">
                            <i class="fas fa-paper-plane"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-0 shadow-sm h-100" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                <div class="card-body text-white">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <div class="text-white-50 small">Tỷ Lệ Mở Email</div>
                            <div class="h2 mb-0">{{ $openRate }}%</div>
                        </div>
                        <div class="fa-2x opacity-50">
                            <i class="fas fa-envelope-open"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="row">
        <!-- Recent Campaigns -->
        <div class="col-lg-8 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-primary">
                            <i class="fas fa-chart-line me-2"></i>Chiến Dịch Gần Đây
                        </h5>
                        <a href="{{ route('email-marketing.campaigns') }}" class="btn btn-outline-primary btn-sm">
                            Xem Tất Cả
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    @if($recentCampaigns->count() > 0)
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="bg-light">
                                    <tr>
                                        <th class="border-0 px-4 py-3">Tên Chiến Dịch</th>
                                        <th class="border-0 px-4 py-3">Webinar</th>
                                        <th class="border-0 px-4 py-3">Trạng Thái</th>
                                        <th class="border-0 px-4 py-3">Email Gửi</th>
                                        <th class="border-0 px-4 py-3">Tỷ Lệ Mở</th>
                                        <th class="border-0 px-4 py-3">Hành Động</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach($recentCampaigns as $campaign)
                                    <tr>
                                        <td class="px-4 py-3">
                                            <div class="fw-bold">{{ $campaign->name }}</div>
                                            <small class="text-muted">{{ $campaign->description }}</small>
                                        </td>
                                        <td class="px-4 py-3">
                                            @if($campaign->webinar)
                                                <span class="badge bg-info">{{ $campaign->webinar->title }}</span>
                                            @else
                                                <span class="badge bg-secondary">Tất cả webinar</span>
                                            @endif
                                        </td>
                                        <td class="px-4 py-3">
                                            @if($campaign->status === 'active')
                                                <span class="badge bg-success">Đang hoạt động</span>
                                            @elseif($campaign->status === 'paused')
                                                <span class="badge bg-warning">Tạm dừng</span>
                                            @elseif($campaign->status === 'completed')
                                                <span class="badge bg-primary">Hoàn thành</span>
                                            @else
                                                <span class="badge bg-secondary">Nháp</span>
                                            @endif
                                        </td>
                                        <td class="px-4 py-3">
                                            {{ $campaign->emailLogs->where('status', 'sent')->count() }}
                                        </td>
                                        <td class="px-4 py-3">
                                            @php
                                                $sent = $campaign->emailLogs->where('status', 'sent')->count();
                                                $opened = $campaign->emailLogs->where('status', 'opened')->count();
                                                $rate = $sent > 0 ? round(($opened / $sent) * 100, 1) : 0;
                                            @endphp
                                            {{ $rate }}%
                                        </td>
                                        <td class="px-4 py-3">
                                            <div class="btn-group btn-group-sm">
                                                <a href="{{ route('email-marketing.campaigns.show', $campaign) }}"
                                                   class="btn btn-outline-primary" title="Xem chi tiết">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="{{ route('email-marketing.campaigns.edit', $campaign) }}"
                                                   class="btn btn-outline-secondary" title="Chỉnh sửa">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    @endforeach
                                </tbody>
                            </table>
                        </div>
                    @else
                        <div class="text-center py-5">
                            <i class="fas fa-envelope fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">Chưa có chiến dịch nào</h5>
                            <p class="text-muted">Tạo chiến dịch email marketing đầu tiên của bạn</p>
                            <a href="{{ route('email-marketing.campaigns.create') }}" class="btn btn-primary">
                                <i class="fas fa-plus me-2"></i>Tạo Chiến Dịch
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Activity Log -->
        <div class="col-lg-4 mb-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="mb-0 text-primary">
                            <i class="fas fa-history me-2"></i>Hoạt Động Gần Đây
                        </h5>
                        <a href="{{ route('email-marketing.logs') }}" class="btn btn-outline-primary btn-sm">
                            Xem Tất Cả
                        </a>
                    </div>
                </div>
                <div class="card-body p-0">
                    @if($recentLogs->count() > 0)
                        <div class="list-group list-group-flush">
                            @foreach($recentLogs->take(8) as $log)
                            <div class="list-group-item border-0 px-4 py-3">
                                <div class="d-flex align-items-center">
                                    <div class="me-3">
                                        @if($log->status === 'sent')
                                            <i class="fas fa-paper-plane text-success"></i>
                                        @elseif($log->status === 'opened')
                                            <i class="fas fa-envelope-open text-primary"></i>
                                        @elseif($log->status === 'failed')
                                            <i class="fas fa-exclamation-triangle text-danger"></i>
                                        @else
                                            <i class="fas fa-clock text-warning"></i>
                                        @endif
                                    </div>
                                    <div class="flex-grow-1">
                                        <div class="fw-bold small">{{ $log->subject }}</div>
                                        <div class="text-muted small">
                                            Gửi đến: {{ $log->recipient_email }}
                                        </div>
                                        <div class="text-muted small">
                                            {{ $log->created_at->diffForHumans() }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <div class="text-center py-4">
                            <i class="fas fa-history fa-2x text-muted mb-2"></i>
                            <p class="text-muted mb-0">Chưa có hoạt động nào</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 py-3">
                    <h5 class="mb-0 text-primary">
                        <i class="fas fa-bolt me-2"></i>Hành Động Nhanh
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('email-marketing.campaigns.create') }}" class="btn btn-outline-primary w-100 py-3">
                                <i class="fas fa-plus fa-2x mb-2 d-block"></i>
                                <div class="fw-bold">Tạo Chiến Dịch</div>
                                <small class="text-muted">Tạo chiến dịch email mới</small>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('email-marketing.campaigns') }}" class="btn btn-outline-info w-100 py-3">
                                <i class="fas fa-list fa-2x mb-2 d-block"></i>
                                <div class="fw-bold">Quản Lý Chiến Dịch</div>
                                <small class="text-muted">Xem tất cả chiến dịch</small>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('email-marketing.analytics') }}" class="btn btn-outline-success w-100 py-3">
                                <i class="fas fa-chart-bar fa-2x mb-2 d-block"></i>
                                <div class="fw-bold">Báo Cáo</div>
                                <small class="text-muted">Xem thống kê chi tiết</small>
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button type="button" class="btn btn-outline-danger w-100 py-3" data-bs-toggle="modal" data-bs-target="#testEmailModal">
                                <i class="fas fa-envelope-open-text fa-2x mb-2 d-block"></i>
                                <div class="fw-bold">Test Email</div>
                                <small class="text-muted">Kiểm tra hệ thống gửi mail</small>
                            </button>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ route('email-marketing.logs') }}" class="btn btn-outline-warning w-100 py-3">
                                <i class="fas fa-history fa-2x mb-2 d-block"></i>
                                <div class="fw-bold">Lịch Sử</div>
                                <small class="text-muted">Xem log hoạt động</small>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Test Email Modal -->
<div class="modal fade" id="testEmailModal" tabindex="-1" aria-labelledby="testEmailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="testEmailModalLabel">
                    <i class="fas fa-envelope-open-text me-2"></i>Test Hệ Thống Email
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Nhập email để kiểm tra xem hệ thống gửi mail có hoạt động tốt không.
                </div>
                <form id="testEmailForm">
                    @csrf
                    <div class="mb-3">
                        <label for="testEmail" class="form-label">
                            <i class="fas fa-envelope me-1"></i>Email nhận test <span class="text-danger">*</span>
                        </label>
                        <input type="email" class="form-control" id="testEmail" name="test_email"
                               placeholder="Nhập email để nhận test..." required>
                        <div class="form-text">Email test sẽ được gửi đến địa chỉ này</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Hủy
                </button>
                <button type="button" class="btn btn-primary" id="sendTestEmailBtn">
                    <i class="fas fa-paper-plane me-1"></i>
                    <span class="btn-text">Gửi Test Email</span>
                    <span class="spinner-border spinner-border-sm d-none" role="status"></span>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

.list-group-item:hover {
    background-color: #f8f9fa;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.modal-content {
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.modal-header {
    border-bottom: none;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const testEmailForm = document.getElementById('testEmailForm');
    const sendTestEmailBtn = document.getElementById('sendTestEmailBtn');
    const testEmailModal = new bootstrap.Modal(document.getElementById('testEmailModal'));

    sendTestEmailBtn.addEventListener('click', function() {
        const formData = new FormData(testEmailForm);
        const testEmail = formData.get('test_email');

        if (!testEmail) {
            alert('Vui lòng nhập email!');
            return;
        }

        // Show loading state
        const btnText = sendTestEmailBtn.querySelector('.btn-text');
        const spinner = sendTestEmailBtn.querySelector('.spinner-border');

        btnText.textContent = 'Đang gửi...';
        spinner.classList.remove('d-none');
        sendTestEmailBtn.disabled = true;

        // Send AJAX request
        fetch('{{ route("email-marketing.test-email") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                test_email: testEmail
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Show success message
                const alertDiv = document.createElement('div');
                alertDiv.className = 'alert alert-success alert-dismissible fade show';
                alertDiv.innerHTML = `
                    <i class="fas fa-check-circle me-2"></i>
                    ${data.message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                `;

                // Insert alert at the top of the page
                const container = document.querySelector('.container-fluid');
                container.insertBefore(alertDiv, container.firstChild);

                // Close modal
                testEmailModal.hide();

                // Reset form
                testEmailForm.reset();

                // Auto remove alert after 5 seconds
                setTimeout(() => {
                    if (alertDiv.parentNode) {
                        alertDiv.remove();
                    }
                }, 5000);
            } else {
                // Show error message
                alert('Lỗi: ' + data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi gửi email test!');
        })
        .finally(() => {
            // Reset button state
            btnText.textContent = 'Gửi Test Email';
            spinner.classList.add('d-none');
            sendTestEmailBtn.disabled = false;
        });
    });

    // Reset form when modal is hidden
    document.getElementById('testEmailModal').addEventListener('hidden.bs.modal', function() {
        testEmailForm.reset();
        const btnText = sendTestEmailBtn.querySelector('.btn-text');
        const spinner = sendTestEmailBtn.querySelector('.spinner-border');
        btnText.textContent = 'Gửi Test Email';
        spinner.classList.add('d-none');
        sendTestEmailBtn.disabled = false;
    });
});
</script>
@endsection

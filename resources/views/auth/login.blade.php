@extends('layouts.app')

@section('content')
<div class="login-page">
    <div class="login-container">
        <div class="login-box">
            <!-- Logo and Heading -->
            <div class="text-center mb-4">
                <div class="logo-container">
                    <i class="fas fa-video video-icon"></i>
                </div>
                <h1 class="login-title">H<PERSON> Thống Quản Lý Webinar</h1>
                <p class="login-subtitle">Đăng nhập để tiếp tục</p>
            </div>
            
            <!-- Login Form -->
            <form method="POST" action="{{ route('login') }}" class="login-form">
                        @csrf

                <!-- Email Input -->
                <div class="form-group">
                    <label for="email">
                        <i class="fas fa-envelope input-icon"></i>
                        Email
                    </label>
                    <input id="email" type="email" class="form-control @error('email') is-invalid @enderror" 
                           name="email" value="{{ old('email') }}" placeholder="Nhập địa chỉ email" 
                           required autocomplete="email" autofocus>
                                @error('email')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                            </div>
                
                <!-- Password Input -->
                <div class="form-group">
                    <label for="password">
                        <i class="fas fa-lock input-icon"></i>
                        Mật khẩu
                    </label>
                    <div class="password-container">
                        <input id="password" type="password" class="form-control @error('password') is-invalid @enderror" 
                               name="password" placeholder="Nhập mật khẩu" required autocomplete="current-password">
                        <i class="fas fa-eye password-toggle" onclick="togglePasswordVisibility()"></i>
                        </div>
                                @error('password')
                                    <span class="invalid-feedback" role="alert">
                                        <strong>{{ $message }}</strong>
                                    </span>
                                @enderror
                        </div>

                <!-- Remember Me Checkbox -->
                <div class="form-group">
                    <div class="custom-control custom-checkbox">
                        <input type="checkbox" class="custom-control-input" name="remember" id="remember" {{ old('remember') ? 'checked' : '' }}>
                        <label class="custom-control-label" for="remember">Ghi nhớ đăng nhập</label>
                            </div>
                        </div>

                <!-- Login Button -->
                <div class="form-group">
                    <button type="submit" class="btn btn-primary btn-block btn-login">
                        <i class="fas fa-sign-in-alt mr-2"></i> Đăng Nhập
                                </button>
                </div>

                <!-- Forgot Password Link -->
                                @if (Route::has('password.request'))
                    <div class="text-center mt-3">
                        <a class="forgot-password-link" href="{{ route('password.request') }}">
                            <i class="fas fa-key mr-1"></i> Quên mật khẩu?
                                    </a>
                    </div>
                                @endif
                    </form>
                </div>
        
        <!-- Footer -->
        <div class="login-footer">
            © {{ date('Y') }} Hệ Thống Quản Lý Webinar. Tất cả quyền được bảo lưu.
            <div class="version-info">Phiên bản 1.0</div>
        </div>
    </div>
</div>
@endsection

@push('styles')
<style>
    body {
        background: linear-gradient(135deg, #FF7D1A 0%, #FF9F4D 100%);
        min-height: 100vh;
        margin: 0;
        overflow-x: hidden;
    }
    
    .login-page {
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 40px 20px;
    }
    
    .login-container {
        width: 100%;
        max-width: 450px;
    }
    
    .login-box {
        background-color: white;
        border-radius: 16px;
        box-shadow: 0 12px 30px rgba(0, 0, 0, 0.1);
        padding: 40px;
        transition: all 0.3s ease;
    }
    
    .login-box:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    }
    
    .logo-container {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, #FF7D1A 0%, #FF9F4D 100%);
        border-radius: 50%;
        margin: 0 auto 20px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    .video-icon {
        font-size: 36px;
        color: white;
    }
    
    .login-title {
        font-size: 24px;
        font-weight: 700;
        color: #333;
        margin-bottom: 8px;
    }
    
    .login-subtitle {
        font-size: 16px;
        color: #666;
        margin-bottom: 30px;
    }
    
    .login-form {
        margin-top: 20px;
    }
    
    .form-group {
        margin-bottom: 25px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #444;
    }
    
    .input-icon {
        color: #FF7D1A;
        margin-right: 10px;
    }
    
    .form-control {
        height: 50px;
        padding: 10px 15px;
        border-radius: 8px;
        border: 2px solid #e1e5eb;
        font-size: 16px;
        transition: all 0.3s;
    }
    
    .form-control:focus {
        border-color: #FF7D1A;
        box-shadow: 0 0 0 0.2rem rgba(255, 125, 26, 0.15);
    }
    
    .password-container {
        position: relative;
    }
    
    .password-toggle {
        position: absolute;
        right: 15px;
        top: 17px;
        color: #666;
        cursor: pointer;
    }
    
    .password-toggle:hover {
        color: #FF7D1A;
    }
    
    .custom-control-input:checked ~ .custom-control-label::before {
        background-color: #FF7D1A;
        border-color: #FF7D1A;
    }
    
    .btn-login {
        background: linear-gradient(135deg, #FF7D1A 0%, #FF9F4D 100%);
        border: none;
        height: 50px;
        font-size: 16px;
        font-weight: 600;
        border-radius: 8px;
        transition: all 0.3s;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
    }
    
    .btn-login:hover {
        background: linear-gradient(135deg, #E56800 0%, #FF8C33 100%);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(255, 125, 26, 0.3);
    }
    
    .btn-login i {
        margin-right: 10px;
    }
    
    .forgot-password-link {
        color: #FF7D1A;
        text-decoration: none;
        font-weight: 600;
        transition: all 0.3s;
    }
    
    .forgot-password-link:hover {
        color: #E56800;
        text-decoration: underline;
    }
    
    .login-footer {
        text-align: center;
        margin-top: 30px;
        font-size: 14px;
        color: rgba(255, 255, 255, 0.8);
    }
    
    .version-info {
        font-size: 12px;
        margin-top: 5px;
        color: rgba(255, 255, 255, 0.6);
    }
    
    @media (max-width: 576px) {
        .login-box {
            padding: 30px 20px;
        }
        
        .logo-container {
            width: 70px;
            height: 70px;
        }
        
        .video-icon {
            font-size: 30px;
        }
        
        .login-title {
            font-size: 20px;
        }
    }
</style>
@endpush

@push('scripts')
<script>
    function togglePasswordVisibility() {
        const passwordInput = document.getElementById('password');
        const passwordToggle = document.querySelector('.password-toggle');
        
        if (passwordInput.type === 'password') {
            passwordInput.type = 'text';
            passwordToggle.classList.remove('fa-eye');
            passwordToggle.classList.add('fa-eye-slash');
        } else {
            passwordInput.type = 'password';
            passwordToggle.classList.remove('fa-eye-slash');
            passwordToggle.classList.add('fa-eye');
        }
    }
    
    document.addEventListener('DOMContentLoaded', function() {
        // Add subtle animation to form elements
        const formGroups = document.querySelectorAll('.form-group');
        formGroups.forEach((group, index) => {
            group.style.opacity = '0';
            group.style.transform = 'translateY(20px)';
            group.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            
            setTimeout(() => {
                group.style.opacity = '1';
                group.style.transform = 'translateY(0)';
            }, 100 + (index * 100));
        });
    });
</script>
@endpush

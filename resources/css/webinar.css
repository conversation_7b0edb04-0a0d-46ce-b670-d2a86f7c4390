:root {
    --primary-color: #3d5af1;
    --secondary-color: #0f1642;
    --accent-color: #ff5a5f;
    --dark-color: #151a30;
    --darker-color: #111426;
    --text-color: #ffffff;
    --text-secondary: #8b93a7;
    --light-color: #f8f9fa;
    --gray-color: #8b93a7;
    --live-color: #ff5a5f;
    --success-color: #4cc9f0;
    --border-radius: 10px;
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    --secondary-color: #6c757d;
    --dark-bg: #0f172a;
    --light-bg: #f8f9fa;
    --text-light: #f1f5f9;
    --text-dark: #1e293b;
    --border-color: #2d3748;
}
body {
    background-color: var(--darker-color);
    font-family: "Inter", sans-serif;
    color: var(--text-color);
    line-height: 1.6;
    min-height: 100vh;
}

.webinar-container {
    max-width: 100%;
    margin: 0;
    padding: 0;
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #0f172a;
    color: #f1f5f9;
}

.top-navbar {
    background-color: var(--darker-color);
    color: white;
    padding: 12px 20px;
    margin-bottom: 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.webinar-title {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.75rem;
    line-height: 1.3;
    color: #60a5fa !important;
}

.status-indicator {
    display: flex;
    align-items: center;
}

.live-badge {
    background-color: var(--live-color);
    color: white;
    padding: 4px 14px;
    border-radius: 50px;
    font-size: 10px;
    font-weight: 600;
    display: flex;
    align-items: center;
}

.live-badge i {
    margin-right: 5px;
    animation: pulse 1.5s infinite;
}

.recording-badge {
    background-color: var(--gray-color);
    color: white;
    padding: 4px 14px;
    border-radius: 50px;
    font-size: 0.75rem;
    font-weight: 600;
}

.viewers-count {
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    color: var(--text-secondary);
}

.viewers-count i {
    margin-right: 5px;
}

.webinar-id {
    font-size: 0.85rem;
    color: var(--text-secondary);
}

.main-content {
    display: flex;
    grid-template-columns: 1fr;
    background-color: var(--dark-color);
    flex-direction: column;
}

.video-section {
    background-color: var(--dark-color);
    position: relative;
    height: 100%;
    display: flex;
    flex-direction: column;
    /* overflow: hidden; */
    flex: 1;
}

.video-container {
    position: relative;
    background-color: #000;
    /* flex-grow: 1; */
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    min-height: 260px;
    height: auto;
}

.video-js {
    width: 100% !important;
    height: 100% !important;
    min-height: 360px !important;
    background-color: #000 !important;
}

.vjs-big-play-button {
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    background-color: var(--primary-color) !important;
}

.webinar-info {
    /* padding: 10px 15px; */
    /* background-color: var(--dark-color); */
    /* border-bottom: 1px solid rgba(255, 255, 255, 0.05); */
}

.webinar-info h3 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 2px;
    color: var(--text-color);
}

.webinar-info .speaker {
    color: var(--text-secondary);
    font-size: 0.85rem;
    margin-bottom: 8px;
}

.webinar-status-alert {
    display: flex;
    align-items: center;
    padding: 5px 12px;
    border-radius: 6px;
    font-size: 0.85rem;
    margin-top: 8px;
    margin-bottom: 0;
    width: fit-content;
    border-radius: 50px;
}

.webinar-status-alert i {
    margin-right: 8px;
    font-size: 12px;
}

.live-alert {
    background-color: rgba(255, 90, 95, 0.1);
    color: var(--live-color);
}

.recording-alert {
    background-color: rgba(139, 147, 167, 0.1);
    color: var(--text-secondary);
}

.chat-section {
    background-color: var(--dark-color);
    display: flex;
    flex-direction: column;
    border-left: 1px solid rgba(255, 255, 255, 0.05);
    /*max-height: calc(100vh - 50px);*/
    /* height: 100vh; */
    width: 100%;
}

.chat-header {
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.chat-header h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
}

.chat-header i {
    margin-right: 8px;
    color: var(--text-color);
}

.chat-options {
    color: var(--text-secondary);
    cursor: pointer;
}

.chat-messages {
    flex-grow: 1;
    overflow-y: auto;
    /* padding: 20px; */
    max-height: calc(100% - 130px);
    scrollbar-width: thin;
    scrollbar-color: var(--gray-color) var(--dark-color);
    padding-bottom: 40px;
}

.chat-message {
    margin-bottom: 15px;
    animation: fadeIn 0.3s ease-out;
    padding: 0 15px;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(5px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.chat-message .meta {
    display: flex;
    align-items: center;
}

.chat-message .meta .name {
    font-weight: 600;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    color: #60a5fa;
}

.chat-message .meta .name i {
    font-size: 0.8rem;
}

.user-badge {
    display: inline-flex;
    margin-right: 6px;
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    color: #fff;
    border-radius: 50%;
    width: 28px;
    height: 28px;
    font-size: 10px;
    align-items: center;
    justify-content: center;
}

.chat-message .meta .time {
    font-size: 0.75rem;
    color: var(--text-secondary);
    /* margin-left: 8px; */
}

.chat-message .content {
    display: inline-block;
    font-size: 0.9rem;
    color: var(--text-color);
    max-width: 90%;
    margin-left: 35px;
}

.chat-input-container {
    padding: 15px;
    border-top: 1px solid rgba(255, 255, 255, 0.05);
}

.chat-input-form {
    /* display: flex;
            gap: 10px; */
}

.chat-input-form .form-control {
    border-radius: 30px;
    padding-left: 20px;
    background-color: rgba(255, 255, 255, 0.05);
    border: none;
    color: var(--text-color);
    height: 45px;
}

.chat-input-form .form-control::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
}

.chat-input-form .form-control:focus {
    box-shadow: none;
    border: 1px solid var(--primary-color);
    background-color: rgba(255, 255, 255, 0.1);
}

.chat-input-form .btn {
    border-radius: 50%;
    width: 40px;
    height: 40px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #0072de;
    color: white;
    gap: 6px;
    font-size: 14px;
}

.system-message {
    background-color: rgba(61, 90, 241, 0.1);
    padding: 10px 15px;
    margin-bottom: 15px;
    font-size: 0.85rem;
    color: var(--text-secondary);
    border-radius: 8px;
    text-align: center;
}

.webinar-ended {
    background-color: var(--dark-color);
    color: white;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 40px;
}

.webinar-ended i {
    font-size: 3rem;
    margin-bottom: 20px;
    color: var(--accent-color);
}

.webinar-ended h2 {
    font-size: 1.75rem;
    margin-bottom: 15px;
}

.webinar-ended p {
    font-size: 1rem;
    opacity: 0.8;
    margin-bottom: 25px;
    color: var(--text-secondary);
}

.discussion-section {
    /*background-color: var(--dark-color);*/
    /*border-top: 1px solid rgba(255, 255, 255, 0.05);*/
    /*padding: 10px;*/
    display: flex;
    align-items: center;
    gap: 10px;
}

.discussion-btn {
    background-color: rgba(255, 255, 255, 0.05);
    color: var(--text-color);
    border: none;
    border-radius: 8px;
    padding: 8px 15px;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.discussion-btn:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--dark-color);
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.2);
}

.webinar-waiting {
    background-color: var(--dark-color);
    color: white;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;
    padding: 40px;
    min-height: 300px;
}

.webinar-waiting i {
    font-size: 3rem;
    margin-bottom: 20px;
    color: var(--primary-color);
}

.webinar-waiting h2 {
    font-size: 1.75rem;
    margin-bottom: 15px;
}

.webinar-waiting p {
    font-size: 1rem;
    opacity: 0.8;
    margin-bottom: 25px;
    color: var(--text-secondary);
}

.countdown-timer {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 20px 0;
}

.countdown-value {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
}

.countdown-label {
    font-size: 1rem;
    color: var(--text-secondary);
}

.waiting-alert {
    background-color: rgba(61, 90, 241, 0.1);
    color: var(--primary-color);
}

/* Livestream player customizations */
.vjs-live .vjs-progress-control {
    display: none !important; /* Hide the progress bar for livestream */
}

.vjs-live .vjs-big-play-button {
    display: none !important; /* Hide the big play button for livestream */
}

.vjs-live .vjs-time-control {
    display: none !important; /* Hide the time display for livestream */
}

.vjs-live .vjs-remaining-time {
    display: none !important; /* Hide remaining time for livestream */
}

.volume-control {
    opacity: 0.7;
    transition: all 0.3s ease;
}

.volume-control:hover {
    opacity: 1;
}

.volume-control > i.fas {
    background-color: rgba(0, 0, 0, 0.7);
    padding: 10px;
    border-radius: 50%;
    font-size: 18px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.volume-notification {
    display: flex;
    align-items: center;
    font-size: 14px;
    padding: 8px 12px;
    border-radius: 5px;
    background-color: rgba(0, 0, 0, 0.8);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
    animation: fade-in-up 0.5s ease-out;
}

@keyframes fade-in-up {
    0% {
        opacity: 0;
        transform: translateY(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.volume-notification i {
    font-size: 16px;
    margin-right: 8px;
}

.volume-text {
    white-space: nowrap;
}

/* Mobile Audio Permission Popup */
.mobile-audio-popup {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: 90%;
    max-width: 320px;
    background: white;
    border-radius: 16px;
    padding: 24px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    z-index: 9999999;
    animation: slideDown 0.3s ease-out;
}

.mobile-audio-popup-content {
    text-align: center;
    width: 100%;
}

.mobile-audio-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #007bff, #0056b3);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 20px;
    box-shadow: 0 8px 20px rgba(0, 123, 255, 0.3);
}

.mobile-audio-icon i {
    font-size: 32px;
    color: white;
}

.mobile-audio-popup h3 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0 0 12px;
}

.mobile-audio-popup p {
    font-size: 16px;
    color: #666;
    line-height: 1.5;
    margin: 0 0 24px;
}

.mobile-audio-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.btn-enable-audio {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    border: none;
    padding: 16px 24px;
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

.btn-enable-audio:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 123, 255, 0.4);
}

.btn-enable-audio:active {
    transform: translateY(0);
}

.btn-skip-audio {
    background: transparent;
    color: #666;
    border: 2px solid #e0e0e0;
    padding: 12px 24px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-skip-audio:hover {
    background: #f5f5f5;
    border-color: #ccc;
    color: #333;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* Hide popup on desktop */
@media (min-width: 768px) {
    .mobile-audio-popup {
        display: none !important;
    }
}

/* Ensure popup is visible on mobile and positioned correctly */
@media (max-width: 767px) {
    .mobile-audio-popup {
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        width: 90%;
        max-width: 320px;
    }
}

/* Additional livestream indicator */
.live-indicator {
    position: absolute;
    top: 10px;
    left: 10px;
    background-color: var(--live-color);
    color: white;
    padding: 3px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
    z-index: 10;
    display: flex;
    align-items: center;
    animation: pulse-bg 2s infinite;
}

.live-indicator i {
    margin-right: 5px;
    font-size: 0.7rem;
    animation: pulse 1.5s infinite;
}

@keyframes pulse-bg {
    0% {
        background-color: rgba(255, 90, 95, 1);
    }
    50% {
        background-color: rgba(255, 90, 95, 0.7);
    }
    100% {
        background-color: rgba(255, 90, 95, 1);
    }
}

/* Native video player styles */
.native-video-player {
    width: 100% !important;
    height: 100% !important;
    /* min-height: 360px !important; */
    background-color: #000 !important;
    display: block;
    object-fit: contain;
}

/* Special case for maintaining aspect ratio while filling container */
@media (min-width: 992px) {
    .position-relative {
        position: relative;
        overflow: hidden;
    }

    .position-relative video {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 100%;
        height: 100%;
        object-fit: contain;
    }
    .main-content {
        height: 100vh;
        overflow: hidden;
    }
}

/* Manual play button for mobile browsers */
.manual-play-button {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(255, 90, 95, 0.8);
    color: white;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px;
    cursor: pointer;
    z-index: 100;
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
    transition: all 0.2s ease;
}

.manual-play-button:hover {
    background-color: rgba(255, 90, 95, 1);
    transform: translate(-50%, -50%) scale(1.05);
}

.manual-play-button.hidden {
    display: none;
}

/* Volume control */
.volume-control {
    position: absolute;
    bottom: 20px;
    left: 20px;
    opacity: 0.7;
    transition: all 0.3s ease;
    z-index: 100;
    cursor: pointer;
}

.volume-control:hover {
    opacity: 1;
}

.volume-control > i.fas {
    background-color: rgba(0, 0, 0, 0.7);
    padding: 10px;
    border-radius: 50%;
    font-size: 18px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* Time display */
.time-display {
    position: absolute;
    bottom: 20px;
    right: 20px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.85rem;
    z-index: 100;
}

/* Livestream duration indicator */
.stream-duration {
    position: absolute;
    bottom: 20px;
    right: 10px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.85rem;
    z-index: 100;
    display: flex;
    align-items: center;
}

.stream-duration i {
    margin-right: 5px;
    color: var(--live-color);
}

/* Debug information */
.debug-info {
    position: absolute;
    top: 60px;
    left: 20px;
    background-color: rgba(0, 0, 0, 0.8);
    color: #4cc9f0;
    padding: 8px 15px;
    border-radius: 4px;
    font-size: 0.8rem;
    z-index: 100;
    font-family: monospace;
    display: none; /* Hidden by default, can be shown with URL param or dev mode */
    border: 1px solid #00b3ff;
    box-shadow: 0 0 10px rgba(0, 179, 255, 0.5);
}

/* Share button and dialog */
.share-btn {
    background-color: rgba(61, 90, 241, 0.1);
    color: var(--primary-color);
    border: none;
    border-radius: 8px;
    padding: 8px 15px;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.share-btn:hover {
    background-color: rgba(61, 90, 241, 0.2);
}

.share-dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: var(--dark-color);
    border-radius: 12px;
    padding: 20px;
    width: 90%;
    max-width: 400px;
    z-index: 1000;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
    display: none;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.share-dialog h4 {
    margin-top: 0;
    font-size: 1.2rem;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.share-dialog h4 .close-btn {
    background: transparent;
    border: none;
    color: var(--text-secondary);
    font-size: 1.2rem;
    cursor: pointer;
    padding: 5px;
}

.share-dialog h4 .close-btn:hover {
    color: white;
}

.share-url {
    display: flex;
    margin: 15px 0;
}

.share-url input {
    flex-grow: 1;
    background-color: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 4px 0 0 4px;
    padding: 8px 12px;
    color: white;
    font-size: 0.9rem;
}

.share-url button {
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0 4px 4px 0;
    padding: 8px 12px;
    cursor: pointer;
}

.share-options {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.share-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--text-secondary);
    width: 60px;
    transition: all 0.2s ease;
}

.share-option:hover {
    color: white;
    transform: translateY(-2px);
}

.share-option .icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
    font-size: 1.2rem;
}

.share-option.facebook .icon {
    background-color: #3b5998;
}

.share-option.twitter .icon {
    background-color: #1da1f2;
}

.share-option.email .icon {
    background-color: #d44638;
}

.share-option.whatsapp .icon {
    background-color: #25d366;
}

.share-option span {
    font-size: 0.8rem;
    text-align: center;
}

.share-dialog-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    display: none;
}

/* Seeded comments styling */
.chat-message.seeded-comment {
    position: relative;
    border-left: 3px solid var(--primary-color);
    animation: fadeIn 0.5s ease-in-out;
}

.chat-message.seeded-comment .meta .name {
    color: var(--primary-color);
    font-weight: 500;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes progress {
    0% {
        width: 5%;
        margin-left: 0;
    }
    50% {
        width: 30%;
        margin-left: 65%;
    }
    100% {
        width: 5%;
        margin-left: 95%;
    }
}

/* Participant list panel */
.participant-list-panel {
    position: fixed;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background-color: var(--dark-color);
    border-radius: 8px;
    width: 250px;
    max-height: 300px;
    display: flex;
    flex-direction: column;
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    z-index: 100;
    overflow: hidden;
}

.participant-list-header {
    padding: 12px 15px;
    font-weight: 600;
    color: white;
    background-color: rgba(255, 255, 255, 0.05);
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
}

.participant-list-header i {
    margin-right: 8px;
    color: var(--primary-color);
}

.participant-count {
    margin-left: auto;
    background-color: var(--primary-color);
    color: white;
    border-radius: 50px;
    font-size: 0.7rem;
    padding: 3px 8px;
}

.participant-list-container {
    overflow-y: auto;
    flex-grow: 1;
    padding: 10px 0;
}

.participant-item {
    padding: 8px 15px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.participant-item:last-child {
    border-bottom: none;
}

.participant-avatar {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    color: white;
    font-weight: 600;
    font-size: 0.8rem;
}

.participant-name {
    color: var(--text-color);
    font-size: 0.9rem;
}

.participant-admin {
    margin-left: 5px;
    font-size: 0.7rem;
    background-color: rgba(255, 90, 95, 0.2);
    color: var(--live-color);
    padding: 2px 6px;
    border-radius: 3px;
}

@media (max-width: 768px) {
    .participant-list-panel {
        display: none; /* Hide on mobile */
    }
}

/* Join notification */
.join-notification {
    padding: 6px 15px;
    margin: 8px 0;
    font-size: 0.85rem;
    color: #4cc9f0; /* Brighter blue color for better visibility */
    background-color: rgba(76, 201, 240, 0.1);
    border-radius: 6px;
    text-align: left;
    border-left: 3px solid #4cc9f0;
}

.join-notification i {
    margin-right: 7px;
    font-size: 0.8rem;
    color: #4cc9f0;
}

/* Fixed join notification container */
.join-notification-fixed {
    background-color: #17225e;
    color: #4cc9f0;
    border-left: 3px solid #4cc9f0;
    padding: 10px 15px;
    border-radius: 4px;
    margin-top: auto;
    /* margin-bottom: 10px; */
    font-size: 0.9rem;
}

.join-notification-fixed i {
    margin-right: 8px;
    color: #4cc9f0;
}

.popup-overlay {
    /* Lớp phủ nền mờ */
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6); /* Độ mờ có thể điều chỉnh */
    z-index: 40; /* Đảm bảo lớp phủ ở dưới popup */
    display: none; /* Ẩn ban đầu */
}
.popup-content {
    /* Nội dung popup */
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.95); /* Hiệu ứng scale ban đầu */
    /* background-color: #1f2937; */
    color: #e5e7eb; /* Màu chữ sáng */
    border-radius: 12px; /* Bo góc lớn hơn */
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 600px; /* Giới hạn chiều rộng tối đa */
    z-index: 50;
    opacity: 0; /* Ẩn ban đầu cho animation */
    transition: opacity 0.3s ease-out, transform 0.3s ease-out;
    overflow-y: auto; /* Cho phép cuộn nếu nội dung dài */
    max-height: 90vh; /* Giới hạn chiều cao tối đa */
    display: none; /* Ẩn ban đầu */
}
.popup-content.open {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
    display: block; /* Hiển thị khi được mở */
}
.noti-seeding {
    position: fixed;
    top: 20px;
    right: 20px;
    left: auto;
    z-index: 9999;
}

.noti-seeding .notification {
    display: flex;
    align-items: center;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    padding: 12px 15px;
    margin-bottom: 10px;
    width: 280px;
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.3s ease;
}
.noti-seeding .notification.show {
    opacity: 1;
    transform: translateX(0);
}
.noti-seeding .avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    margin-right: 12px;
    flex-shrink: 0;
}
.noti-seeding .avatar-purple {
    background-color: #8a2be2;
}
.noti-seeding .avatar-blue {
    background-color: #1e90ff;
}
.noti-seeding .avatar-green {
    background-color: #32cd32;
}
.noti-seeding .content {
    flex: 1;
}
.noti-seeding .email {
    font-size: 14px;
    font-weight: bold;
    margin-bottom: 3px;
}
.noti-seeding .message {
    font-size: 13px;
    color: #666;
    line-height: 1.4;
}
.noti-seeding .badge-seeding {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
}
.noti-seeding .badge-free {
    background-color: #e0ffff;
    color: #00bfff;
}
.noti-seeding .badge-pro {
    background-color: #ffd700;
    color: #b8860b;
}
.noti-seeding .badge-premium {
    background-color: #ffd700;
    color: #8b4513;
}
.noti-seeding .badge-completed {
    background-color: #98fb98;
    color: #006400;
}
.noti-seeding .action-icons {
    display: flex;
    margin-top: 8px;
}
.noti-seeding .icon {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    margin-right: 8px;
}
.noti-seeding .icon-sound {
    background-color: #9932cc;
    color: white;
}
.noti-seeding .icon-messenger {
    background-color: #4169e1;
    color: white;
}
.noti-seeding .achievement {
    background-color: #f8f9fa;
    padding: 8px;
    margin-top: 8px;
    border-radius: 6px;
    font-size: 12px;
    font-weight: bold;
}

.popup-overlay {
    background-color: rgba(0, 0, 0, 0.5);
}

.popup-content {
    transform: translateY(20px);
    transition: transform 0.3s ease;
}

.popup.active .popup-content {
    transform: translateY(0);
}

.modal-regis .step.completed .step-circle {
    background-color: #ff8c00;
    border-color: #ff8c00;
    color: white;
}

.modal-regis .payment-header {
    margin-bottom: 10px;
}

.modal-regis .payment-body {
    padding: 20px 40px;
}

.modal-regis .price-info {
    display: flex;
    justify-content: space-between;
    /* margin-bottom: 5px;
             */
}

.modal-regis .total-price {
    display: flex;
    justify-content: space-between;
    padding: 15px 0;
    margin-top: 10px;
    border-top: 1px dashed #e0e0e0;
}

.modal-regis .price {
    color: #ff5722;
    font-weight: bold;
}

.modal-regis .payment-methods-container {
    background-color: white;
    padding: 20px 15px;
    border-radius: 15px;
    border: 1px solid #ffd8a8;
}

.modal-regis .method-option {
    border-radius: 10px;
    margin-bottom: 20px;
}

.modal-regis .method-title {
    /* font-weight: bold;
             */
    margin-bottom: 15px;
    color: #333;
}

.modal-regis .qr-container {
    position: relative;
}

.modal-regis .bank-info {
    margin-bottom: 10px;
    font-size: 14px;
}

.modal-regis .bank-info-row {
    display: flex;
    justify-content: space-between;
    border-bottom: 1px solid #fff;
    padding: 8px 0;
    color: #333;
}

.modal-regis .bank-info-label {
    color: #333;
}

.modal-regis .bank-info-value {
    font-weight: 600;
    color: #333;
}

.modal-regis .warning-box {
    background-color: #fff9f0;
    border: 1px dashed #ffca80;
    border-radius: 8px;
    padding: 10px;
    margin: 15px 0;
    border: 1px dashed var(--Primary-Color, #fa8128);
    background: #fff9ec;
    font-size: 12px;
    color: #333;
}

.modal-regis .warning-icon {
    color: #ff8c00;
    margin-right: 5px;
}

.modal-regis .download-btn {
    background-color: #105ce4;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 5px 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    font-size: 12px;
}

.modal-regis .timer-container {
    align-items: center;
    background-color: #fff5eb;
    border-radius: 10px;
    padding: 10px 15px;
    /* margin-top: 20px; */
    display: flex;
    justify-content: space-between;
    border-radius: 15px;
    background: linear-gradient(180deg, #fff0f0 0%, #fed 100%);
    font-size: 16px;
    color: #333;
}

.modal-regis .timer {
    color: #105ce4;
    font-weight: bold;
    font-size: 24px;
}

.modal-regis .status-container {
    text-align: center;
    margin-top: 15px;
    color: #666;
}

.modal-regis .status-container .loading-icon {
    color: #fa8128;
}

.modal-regis .loading-icon {
    display: inline-block;
    margin-left: 5px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Custom spinner */
.custom-spinner {
    display: inline-block;
    width: 15px;
    height: 15px;
    border: 2px solid rgba(250, 129, 40, 0.3);
    border-radius: 50%;
    border-top-color: #fa8128;
    animation: spin 1s ease-in-out infinite;
    vertical-align: middle;
}

#qrcode {
    background-color: white;
    padding: 8px;
    border-radius: 4px;
}

.scanner {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(
        to right,
        rgba(255, 140, 0, 0) 0%,
        rgba(255, 140, 0, 0.4) 50%,
        rgba(255, 140, 0, 0) 100%
    );
    box-shadow: 0 0 4px rgba(255, 140, 0, 0.4), 0 0 8px rgba(255, 140, 0, 0.3);
    z-index: 10;
    animation: scan 3s infinite linear;
    opacity: 0.6;
}

@keyframes scan {
    0% {
        top: 0;
    }

    50% {
        top: calc(100% - 2px);
    }

    100% {
        top: 0;
    }
}

#qrcode::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        to bottom,
        rgba(255, 140, 0, 0.03) 0%,
        rgba(255, 140, 0, 0) 40%,
        rgba(255, 140, 0, 0) 60%,
        rgba(255, 140, 0, 0.03) 100%
    );
    pointer-events: none;
    z-index: 5;
}

.modal-regis .img-bank {
    position: absolute;
    width: calc(100% - 15px);
    top: 45%;
    left: 50%;
    transform: translate(-50%, -50%);
    object-fit: contain;
}

.modal-regis .border-bank {
    aspect-ratio: 1;
    width: 100%;
}

.modal-regis .body-method-option {
    background: #f7f7f7;
    padding: 0 10px;
}

.modal-regis #btn-checkout-regis {
    border-radius: 10px;
    background: var(
        --gradient-2,
        linear-gradient(270deg, #eb2805 0%, #fbc30b 100%)
    );
    border: none;
    display: block;
    width: 100%;
    height: 56px;
    font-size: 18px;
    text-transform: uppercase;
    font-weight: bold;
    color: #fff;
}

.modal-regis .phone-group {
    border: 1px solid #dadada;
    border-radius: 5px;
}

.modal-regis .phone-group input {
    border: none;
}

.modal-regis .phone-group button {
    border: none;
    border-right: 1px solid #dadada;
    border-radius: 0;
    display: flex;
    width: 110px;
    font-size: 16px;
    align-items: center;
    gap: 6px;
    justify-content: center;
}

.modal-regis input#couponInput {
    color: #dc3f2e;
}
.popup-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
    opacity: 0;
    transition: opacity 300ms ease;
}
.popup-overlay.active {
    opacity: 1;
}
[id$="Popup"] {
    display: none;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.8);
    z-index: 999999;
    opacity: 0;
    transition: all 300ms ease;
    max-width: 95%;
    max-height: 90vh;
    overflow-y: auto;
    border-radius: 10px;
    overflow: hidden;
}
[id$="Popup"].modal-open {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}
[id$="Popup"].modal-close {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
}

.text-success {
    color: #4caf50;
}

.text-danger {
    color: #f44336;
}

.text-red-500 {
    color: #ef4444;
}

.opacity-50 {
    opacity: 0.5;
}

/* Webinar info section styling */
.speaker {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    color: #e2e8f0;
}

.host-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 28px;
    height: 28px;
    background: var(--primary-color);
    border-radius: 50%;
    margin-right: 8px;
    font-size: 0.8rem;
    color: white;
    box-shadow: 0 0 10px rgba(61, 90, 241, 0.5);
}

.host-name {
    font-weight: 600;
    letter-spacing: 0.3px;
}

@keyframes flashPulse {
    0%,
    100% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.6;
    }
}
@keyframes attentionBounce {
    0%,
    100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-5px);
    }
}

@media (min-width: 992px) {
    .main-content {
        /*grid-template-columns: 9fr 3fr;*/
        flex-direction: row;
    }
    .chat-section {
        width: 25%;
        height: 100vh;
        position: relative;
    }
    .video-container {
        height: 100%;
    }
    #join-notification-container {
        position: absolute;
        bottom: 75px;
        width: 100%;
    }
}

@media (max-width: 768px) {
    .chat-section {
        position: fixed;
        bottom: 0;
        height: calc(100% - var(--top));
        width: 100%;
        z-index: 1000;
    }
    .modal-image-product {
        height: 150px !important;
    }
    #comment-form {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    #comment-form textarea {
        height: 50px;
    }
    .chat-input-form .btn {
        margin: 0;
    }
    .chat-messages {
        height: calc(100% - 130px);
        max-height: calc(100% - 130px);
    }
    #join-notification-container {
        position: fixed;
        width: 100%;
        bottom: 80px;
    }
    .video-section {
        position: fixed;
        top: 0;
        width: 100%;
    }
    .modal-image-img {
        height: 130px !important;
    }

    .registerForm form input {
        padding: 8px !important;
        font-size: 14px;
    }

    .modal-title {
        font-size: 16px !important;
        padding: 12px 10px !important;
    }

    #reopenOffer {
        z-index: 9999;
    }

    .submitFormButton {
        padding: 12px !important;
    }
}
.name-time {
    display: flex;
    gap: 8px;
    align-items: end;
}

button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

#questionPopup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) scale(0.8);
    z-index: 1001;
    border-radius: 12px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    max-height: 90vh;
    overflow-y: auto;
    display: none;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

#questionPopup.modal-open {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
}

#questionPopup.modal-close {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
}

/* Custom Slider Styles */
.slider {
    -webkit-appearance: none;
    background: #4b5563;
    outline: none;
    border-radius: 8px;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #3b82f6;
    cursor: pointer;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.slider::-webkit-slider-thumb:hover {
    background: #2563eb;
    transform: scale(1.1);
}

.slider::-moz-range-thumb {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: #3b82f6;
    cursor: pointer;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    transition: all 0.2s ease;
}

.slider::-moz-range-thumb:hover {
    background: #2563eb;
    transform: scale(1.1);
}

/* Quick Poll Card Styles */
.grid label input[type="radio"]:checked + div {
    transform: scale(1.1);
}

.grid label:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

/* Form Input Enhancements */
input[type="radio"]:checked,
input[type="checkbox"]:checked {
    background-color: #3b82f6 !important;
    border-color: #3b82f6 !important;
}

input[type="radio"]:focus,
input[type="checkbox"]:focus {
    ring-color: #3b82f6 !important;
    border-color: #3b82f6 !important;
}

textarea:focus {
    ring-color: #3b82f6 !important;
    border-color: #3b82f6 !important;
}

/* Animation for form elements */
.interaction-modal-content > * {
    animation: slideInUp 0.4s ease forwards;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Button hover effects */
button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

button:active {
    transform: translateY(0);
}

/* Disabled button styles */
button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    #questionPopup {
        width: 95% !important;
        max-width: none;
        margin: 0 auto;
    }

    .grid {
        grid-template-columns: 1fr !important;
    }

    #interactionTriggers {
        right: 8px !important;
        top: 8px !important;
    }

    #interactionTriggers .max-w-xs {
        max-width: 280px;
    }
}

/* Loading spinner */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #ffffff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* Success/Error message animations */
.bg-red-600,
.bg-green-600 {
    animation: slideInDown 0.3s ease;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Scroll bar styling for modal */
#questionPopup::-webkit-scrollbar {
    width: 6px;
}

#questionPopup::-webkit-scrollbar-track {
    background: #374151;
    border-radius: 3px;
}

#questionPopup::-webkit-scrollbar-thumb {
    background: #6b7280;
    border-radius: 3px;
}

#questionPopup::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

#questionPopup label {
    font-size: 16px;
}
input {
    outline: none !important;
}
.interaction-modal-content label {
    padding: 10px 5px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

/* Interaction Modal Styles */
.interaction-modal-content {
    max-height: 70vh;
    overflow-y: auto;
}

/* Custom Radio and Checkbox Styles */
.interaction-modal-content input[type="radio"],
.interaction-modal-content input[type="checkbox"] {
    width: 18px;
    height: 18px;
    accent-color: #3b82f6;
}

.interaction-modal-content input[type="radio"]:checked,
.interaction-modal-content input[type="checkbox"]:checked {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

/* Hover effects for options */
.interaction-modal-content label:hover {
    background-color: rgba(55, 65, 81, 0.7);
    transform: translateX(2px);
    transition: all 0.3s ease;
}

/* Rating scale styles */
.interaction-modal-content input[type="radio"] + span {
    transition: all 0.3s ease;
}

.interaction-modal-content input[type="radio"]:checked + span {
    color: #fbbf24;
    transform: scale(1.05);
}

/* Slider styles */
.slider {
    -webkit-appearance: none;
    appearance: none;
    background: #374151;
    outline: none;
    border-radius: 8px;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: #3b82f6;
    cursor: pointer;
    border-radius: 50%;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: #3b82f6;
    cursor: pointer;
    border-radius: 50%;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Textarea styles */
.interaction-modal-content textarea {
    resize: vertical;
    min-height: 100px;
}

.interaction-modal-content textarea:focus {
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Button styles */
.btn-submit {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    transition: all 0.3s ease;
}

.btn-submit:hover {
    background: linear-gradient(135deg, #1d4ed8, #1e40af);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.btn-submit:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-cancel {
    transition: all 0.3s ease;
}

.btn-cancel:hover {
    background-color: #4b5563;
    transform: translateY(-1px);
}

/* Message toast animations */
.message-toast {
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Modal content animations */
.modal-body {
    animation: fadeInUp 0.4s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Progress indicator */
.progress-indicator {
    width: 100%;
    height: 4px;
    background-color: #374151;
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 1rem;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, #3b82f6, #1d4ed8);
    transition: width 0.3s ease;
}

/* Character counter */
.char-count {
    font-weight: 600;
}

/* Multiple select validation */
.validation-message {
    color: #fbbf24;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

/* Responsive design */
@media (max-width: 640px) {
    .interaction-modal-content {
        padding: 1rem;
    }

    .modal-body {
        font-size: 0.875rem;
    }

    .btn-submit,
    .btn-cancel {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .flex.justify-end.space-x-3 {
        flex-direction: column;
        space-x: 0;
    }
}

/* Loading state */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: "";
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* Custom scrollbar for modal content */
.interaction-modal-content::-webkit-scrollbar {
    width: 6px;
}

.interaction-modal-content::-webkit-scrollbar-track {
    background: #374151;
    border-radius: 3px;
}

.interaction-modal-content::-webkit-scrollbar-thumb {
    background: #6b7280;
    border-radius: 3px;
}

.interaction-modal-content::-webkit-scrollbar-thumb:hover {
    background: #9ca3af;
}

/* Icon animations */
.ani-icon {
    display: inline-block;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%,
    20%,
    50%,
    80%,
    100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Focus styles for accessibility */
.interaction-modal-content input:focus,
.interaction-modal-content textarea:focus,
.interaction-modal-content button:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    .interaction-modal-content {
        border: 2px solid #ffffff;
    }

    .interaction-modal-content label:hover {
        background-color: #000000;
        color: #ffffff;
    }
}
